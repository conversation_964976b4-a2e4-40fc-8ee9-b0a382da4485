import { useQuery } from "@tanstack/react-query";
import { Plus, RotateCcw, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useService } from "~/config/context/serviceProvider";
import { useAppForm } from "~/core/components/form/form";
import { getErrorResult } from "~/core/utils/effectErrors";
import { CategoryCode } from "~/modules/category/service/model/category";
import { productOptionsByCategoryCode } from "~/modules/product/hooks/product-options";
import type { Product } from "~/modules/product/service/model/product";
import { ProductionOrderSimulationSchema } from "~/modules/production-order-simulation/components/ProductionOrderSimulation/schema";
import type {
	MultiProductCalculation,
	ProductOrderItem,
} from "~/modules/production-order-simulation/components/ProductionOrderSimulation/types";
import { recipeOptions } from "~/modules/recipe/hooks/recipe-options";
import type { Recipe } from "~/modules/recipe/service/model/recipe";

export default function ProductionOrderSimulation() {
	const service = useService();
	const [calculation, setCalculation] =
		useState<MultiProductCalculation | null>(null);

	const {
		data: products = [],
		isPending: isLoadingProducts,
		isError: isProductsError,
		error: productsError,
	} = useQuery(productOptionsByCategoryCode(service, CategoryCode.PRODUCTS));

	const { data: materials = [] } = useQuery(
		productOptionsByCategoryCode(service, CategoryCode.MATERIALS),
	);

	const {
		data: recipes = [],
		isPending: isLoadingRecipes,
		isError: isRecipesError,
		error: recipesError,
	} = useQuery(recipeOptions(service));

	useEffect(() => {
		if (productsError) {
			console.log(getErrorResult(productsError).error);
		}
		if (recipesError) {
			console.log(getErrorResult(recipesError).error);
		}
	}, [productsError, recipesError]);

	const form = useAppForm({
		defaultValues: {
			products: [{ productId: "", quantity: 1 }],
		},
		validators: {
			onChange: ProductionOrderSimulationSchema,
		},
		onSubmit: ({ value }) => {
			const result = calculateMultiProductOrder(
				value.products,
				products,
				materials,
				recipes,
			);
			setCalculation(result);
		},
	});

	const resetForm = () => {
		form.reset();
		setCalculation(null);
	};

	// Handle empty products state
	if (!isLoadingProducts && !isProductsError && products.length === 0) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="alert alert-info">
							<div>
								<h4 className="font-semibold">No hay productos disponibles</h4>
								<p>
									No se encontraron productos en el sistema. Debe crear
									productos antes de poder simular órdenes de producción.
								</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Handle loading states
	if (isLoadingProducts || isLoadingRecipes) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="flex justify-center p-8">
							<span className="loading loading-spinner loading-lg" />
						</div>
					</div>
				</div>
			</div>
		);
	}

	// Handle error states
	if (isProductsError || isRecipesError) {
		return (
			<div className="container mx-auto">
				<div className="card bg-base-100 shadow-xl">
					<div className="card-body">
						<div className="alert alert-error">
							<span>
								Error al cargar datos:{" "}
								{isProductsError && productsError
									? getErrorResult(productsError).error.message
									: isRecipesError && recipesError
										? getErrorResult(recipesError).error.message
										: "Error desconocido"}
							</span>
						</div>
					</div>
				</div>
			</div>
		);
	}

	return (
		<div className="container mx-auto space-y-6">
			<div className="card bg-base-100 shadow-xl">
				<div className="card-body">
					<h2 className="card-title mb-6 text-2xl">
						Simulación de Orden de Producción
					</h2>
					<form
						onSubmit={(e) => {
							e.preventDefault();
							form.handleSubmit();
						}}
					>
						<form.AppForm>
							<div className="mb-6">
								<h3 className="mb-4 font-semibold text-lg">Productos</h3>
								<form.AppField
									name="products"
									children={(field) => (
										<div className="space-y-4">
											{field.state.value.map(
												(_: ProductOrderItem, index: number) => (
													<div
														key={`product-${index}-${Date.now()}`}
														className="grid grid-cols-1 gap-4 rounded-lg border p-4 md:grid-cols-3"
													>
														<form.AppField
															name={`products[${index}].productId`}
															children={({ FSComboBoxField }) => (
																<FSComboBoxField
																	label="Producto"
																	placeholder="Seleccione un producto"
																	options={products.map((product) => ({
																		value: product.id,
																		label: `${product.name} (${product.code})`,
																	}))}
																	isLoading={isLoadingProducts}
																/>
															)}
														/>

														<form.AppField
															name={`products[${index}].quantity`}
															children={({ FSTextField }) => (
																<FSTextField
																	label="Cantidad"
																	placeholder="Ingrese la cantidad"
																	type="number"
																/>
															)}
														/>

														<div className="flex items-end">
															{field.state.value.length > 1 && (
																<button
																	type="button"
																	className="btn btn-error btn-sm"
																	onClick={() => {
																		const newProducts =
																			field.state.value.filter(
																				(_: ProductOrderItem, i: number) =>
																					i !== index,
																			);
																		field.setValue(newProducts);
																	}}
																>
																	<Trash2 size={16} />
																	Eliminar
																</button>
															)}
														</div>
													</div>
												),
											)}

											<button
												type="button"
												className="btn btn-outline btn-sm"
												onClick={() => {
													const newProducts = [
														...field.state.value,
														{ productId: "", quantity: 1 },
													];
													field.setValue(newProducts);
												}}
											>
												<Plus size={16} />
												Agregar Producto
											</button>
										</div>
									)}
								/>
							</div>

							<div className="flex gap-4">
								<form.SubscribeButton label="Calcular" />
								<button
									type="button"
									className="btn btn-ghost"
									onClick={resetForm}
								>
									<RotateCcw size={16} />
									Limpiar
								</button>
							</div>
						</form.AppForm>
					</form>
				</div>
			</div>

			{calculation && (
				<div className="space-y-6">
					{/* Recipe Calculations */}
					{calculation.recipes.length > 0 && (
						<div className="card bg-base-100 shadow-xl">
							<div className="card-body">
								<h3 className="card-title mb-4 text-xl">Recetas Utilizadas</h3>
								{calculation.recipes.map((recipe) => (
									<div key={recipe.recipeId} className="mb-6">
										<div className="alert alert-info mb-4">
											<div>
												<h4 className="font-semibold">
													Receta: {recipe.recipeName} ({recipe.recipeType})
												</h4>
												<p>
													Tamaño de lote: {recipe.batchSize} | Lotes necesarios:{" "}
													{recipe.batchesNeeded.toFixed(2)}
												</p>
												<div className="mt-2">
													<strong>Productos en esta receta:</strong>
													{recipe.productsInRecipe.map((product) => (
														<span
															key={product.productId}
															className="badge badge-outline ml-2"
														>
															{product.productName}: {product.requestedQuantity}
														</span>
													))}
												</div>
											</div>
										</div>

										{recipe.components.length > 0 && (
											<div className="overflow-x-auto">
												<table className="table-zebra table w-full">
													<thead>
														<tr>
															<th>Componente</th>
															<th>Cantidad por Lote</th>
															<th>Cantidad Total</th>
														</tr>
													</thead>
													<tbody>
														{recipe.components.map((component) => (
															<tr key={component.productId}>
																<td>{component.productName}</td>
																<td>{component.quantityPerBatch}</td>
																<td className="font-semibold">
																	{component.totalQuantity.toFixed(2)}
																</td>
															</tr>
														))}
													</tbody>
												</table>
											</div>
										)}
									</div>
								))}
							</div>
						</div>
					)}

					{/* Aggregated Components */}
					{calculation.aggregatedComponents.length > 0 && (
						<div className="card bg-base-100 shadow-xl">
							<div className="card-body">
								<h3 className="card-title mb-4 text-xl">
									Resumen de Componentes (Agregados)
								</h3>
								<div className="overflow-x-auto">
									<table className="table-zebra table w-full">
										<thead>
											<tr>
												<th>Componente</th>
												<th>Cantidad Total</th>
												<th>Fuentes</th>
											</tr>
										</thead>
										<tbody>
											{calculation.aggregatedComponents.map((component) => (
												<tr key={component.productId}>
													<td>{component.productName}</td>
													<td className="font-semibold">
														{component.totalQuantity.toFixed(2)}
													</td>
													<td>
														{component.sources.map((source) => (
															<div
																key={source.recipeId}
																className="badge badge-outline mr-1"
															>
																{source.recipeName}:{" "}
																{source.quantity.toFixed(2)}
															</div>
														))}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</div>
						</div>
					)}

					{/* Aggregated Materials */}
					{calculation.aggregatedMaterials.length > 0 && (
						<div className="card bg-base-100 shadow-xl">
							<div className="card-body">
								<h3 className="card-title mb-4 text-xl">
									Resumen de Materiales (Agregados)
								</h3>
								<div className="overflow-x-auto">
									<table className="table-zebra table w-full">
										<thead>
											<tr>
												<th>Material</th>
												<th>Cantidad Total</th>
												<th>Fuentes</th>
											</tr>
										</thead>
										<tbody>
											{calculation.aggregatedMaterials.map((material) => (
												<tr key={material.materialId}>
													<td>{material.materialName}</td>
													<td className="font-semibold">
														{material.totalQuantity.toFixed(2)}
													</td>
													<td>
														{material.sources.map((source) => (
															<div
																key={source.productId}
																className="badge badge-outline mr-1"
															>
																{source.productName}:{" "}
																{source.quantity.toFixed(2)}
															</div>
														))}
													</td>
												</tr>
											))}
										</tbody>
									</table>
								</div>
							</div>
						</div>
					)}

					{/* No Data Warning */}
					{calculation.recipes.length === 0 &&
						calculation.productMaterials.length === 0 && (
							<div className="card bg-base-100 shadow-xl">
								<div className="card-body">
									<div className="alert alert-warning">
										<div>
											<h4 className="font-semibold">
												Sin componentes ni materiales
											</h4>
											<p>
												Los productos seleccionados no tienen recetas asociadas
												ni información de producción configurada. Para obtener
												cálculos de producción, asegúrese de:
											</p>
											<ul className="mt-2 list-inside list-disc space-y-1">
												<li>Crear recetas que incluyan estos productos</li>
												<li>
													Configurar información de producción con materiales
												</li>
											</ul>
										</div>
									</div>
								</div>
							</div>
						)}
				</div>
			)}
		</div>
	);
}

function calculateMultiProductOrder(
	orderItems: ProductOrderItem[],
	allProducts: Product[],
	allMaterials: Product[],
	allRecipes: Recipe[],
): MultiProductCalculation {
	const result: MultiProductCalculation = {
		recipes: [],
		productMaterials: [],
		aggregatedComponents: [],
		aggregatedMaterials: [],
	};

	// Validate inputs
	if (!orderItems.length || !allProducts.length) {
		return result;
	}

	// Group products by recipe to optimize batch calculations
	const recipeGroups = new Map<
		string,
		{ recipe: Recipe; products: Array<{ product: Product; quantity: number }> }
	>();

	// Categorize products by their recipes
	for (const item of orderItems) {
		const product = allProducts.find((p) => p.id === item.productId);
		if (!product || item.quantity <= 0) continue;

		const recipe = allRecipes.find((r) =>
			r.products.some((p) => p.id === product.id),
		);

		if (recipe) {
			if (!recipeGroups.has(recipe.id)) {
				recipeGroups.set(recipe.id, { recipe, products: [] });
			}
			recipeGroups
				.get(recipe.id)
				?.products.push({ product, quantity: item.quantity });
		}
		// Note: Products without recipes will be handled through their production info materials
	}

	// Calculate recipe-based production
	for (const [, group] of recipeGroups) {
		const { recipe, products } = group;

		// Calculate total batches needed for this recipe
		let batchesNeeded = 0;
		if (recipe.type === "bulk" && recipe.batchSize > 0) {
			// For bulk recipes, calculate minimum batches needed
			const totalQuantityNeeded = products.reduce(
				(sum, p) => sum + p.quantity,
				0,
			);
			batchesNeeded = totalQuantityNeeded / recipe.batchSize;
		} else {
			// For unit recipes, sum all quantities
			batchesNeeded = products.reduce((sum, p) => sum + p.quantity, 0);
		}

		const recipeCalculation = {
			recipeId: recipe.id,
			recipeName: recipe.name,
			recipeType: recipe.type,
			batchSize: recipe.batchSize,
			batchesNeeded,
			productsInRecipe: products.map((p) => ({
				productId: p.product.id,
				productName: p.product.name,
				requestedQuantity: p.quantity,
			})),
			components: recipe.components
				.filter((component) => component.quantity > 0)
				.map((component) => ({
					productId: component.product.id,
					productName: component.product.name,
					quantityPerBatch: component.quantity,
					totalQuantity: component.quantity * batchesNeeded,
				})),
		};

		result.recipes.push(recipeCalculation);
	}

	// Calculate production info materials for all products
	for (const item of orderItems) {
		const product = allProducts.find((p) => p.id === item.productId);
		if (!product || item.quantity <= 0 || !product.productionInfo?.materials)
			continue;

		for (const material of product.productionInfo.materials) {
			if (material.quantity <= 0) continue;

			const materialProduct = allMaterials.find(
				(m) => m.id === material.productId,
			);
			if (!materialProduct) continue;

			result.productMaterials.push({
				productId: product.id,
				productName: product.name,
				materialId: material.productId,
				materialName: materialProduct.name,
				quantityPerUnit: material.quantity,
				totalQuantity: material.quantity * item.quantity,
				requestedQuantity: item.quantity,
			});
		}
	}

	// Aggregate components from all recipes
	const componentMap = new Map<
		string,
		{
			productName: string;
			totalQuantity: number;
			sources: Array<{
				recipeId: string;
				recipeName: string;
				quantity: number;
			}>;
		}
	>();

	for (const recipe of result.recipes) {
		for (const component of recipe.components) {
			if (!componentMap.has(component.productId)) {
				componentMap.set(component.productId, {
					productName: component.productName,
					totalQuantity: 0,
					sources: [],
				});
			}

			const existing = componentMap.get(component.productId);
			if (existing) {
				existing.totalQuantity += component.totalQuantity;
				existing.sources.push({
					recipeId: recipe.recipeId,
					recipeName: recipe.recipeName,
					quantity: component.totalQuantity,
				});
			}
		}
	}

	result.aggregatedComponents = Array.from(componentMap.entries()).map(
		([productId, data]) => ({
			productId,
			productName: data.productName,
			totalQuantity: data.totalQuantity,
			sources: data.sources,
		}),
	);

	// Aggregate materials from production info
	const materialMap = new Map<
		string,
		{
			materialName: string;
			totalQuantity: number;
			sources: Array<{
				productId: string;
				productName: string;
				quantity: number;
			}>;
		}
	>();

	for (const material of result.productMaterials) {
		if (!materialMap.has(material.materialId)) {
			materialMap.set(material.materialId, {
				materialName: material.materialName,
				totalQuantity: 0,
				sources: [],
			});
		}

		const existing = materialMap.get(material.materialId);
		if (existing) {
			existing.totalQuantity += material.totalQuantity;
			existing.sources.push({
				productId: material.productId,
				productName: material.productName,
				quantity: material.totalQuantity,
			});
		}
	}

	result.aggregatedMaterials = Array.from(materialMap.entries()).map(
		([materialId, data]) => ({
			materialId,
			materialName: data.materialName,
			totalQuantity: data.totalQuantity,
			sources: data.sources,
		}),
	);

	return result;
}
